"""
Test script to verify the Wind Farm Turbine Investigation Application functionality.
"""

import pandas as pd
import pickle
import sys
import os

def test_data_loader():
    """Test the data loader functionality."""
    print("🧪 Testing Data Loader...")

    try:
        from src.utils.data_loader import DataLoader

        # Test with sample data
        data_loader = DataLoader()

        if os.path.exists('sample_turbine_data.pkl'):
            success, message = data_loader.load_pkl_data('sample_turbine_data.pkl')
            if success:
                print(f"   ✅ Data loading: {message}")
            else:
                print(f"   ❌ Data loading failed: {message}")
                return False
        else:
            print("   ⚠️ Sample data not found. Run sample_data_generator.py first.")
            return False

        # Test layout data
        if os.path.exists('sample_layout_data.csv'):
            success, message = data_loader.load_layout_data('sample_layout_data.csv')
            if success:
                print(f"   ✅ Layout loading: {message}")
            else:
                print(f"   ❌ Layout loading failed: {message}")

        # Test data summary
        summary = data_loader.get_data_summary()
        print(f"   ✅ Data summary: {summary['total_records']} records, {summary['unique_turbines']} turbines")

        return True

    except Exception as e:
        print(f"   ❌ Data loader test failed: {str(e)}")
        return False


def test_operational_state_classifier():
    """Test the operational state classification."""
    print("🧪 Testing Operational State Classifier...")

    try:
        from src.utils.data_loader import DataLoader
        from src.utils.operational_state import OperationalStateClassifier

        # Load data
        data_loader = DataLoader()
        if not os.path.exists('sample_turbine_data.pkl'):
            print("   ⚠️ Sample data not found. Run sample_data_generator.py first.")
            return False

        success, message = data_loader.load_pkl_data('sample_turbine_data.pkl')
        if not success:
            print(f"   ❌ Failed to load data: {message}")
            return False

        # Test classification
        classifier = OperationalStateClassifier(data_loader)

        # Take a small sample for testing
        sample_data = data_loader.data.head(1000)
        classified_data = classifier.classify_turbine_states(sample_data)

        # Check if classification columns were added
        required_columns = ['operational_state', 'state_category', 'state_subcategory', 'state_reason', 'is_producing']
        for col in required_columns:
            if col not in classified_data.columns:
                print(f"   ❌ Missing classification column: {col}")
                return False

        # Check state distribution
        state_counts = classified_data['operational_state'].value_counts()
        print(f"   ✅ Classification completed. State distribution:")
        for state, count in state_counts.items():
            print(f"      {state}: {count} records")

        return True

    except Exception as e:
        print(f"   ❌ Operational state classifier test failed: {str(e)}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("🧪 Testing Configuration...")

    try:
        from src.utils.config import (
            PRODUCTION_THRESHOLD_KW, CUT_IN_WIND_SPEED,
            OPERATIONAL_STATES, REQUIRED_COLUMNS
        )

        print(f"   ✅ Production threshold: {PRODUCTION_THRESHOLD_KW} kW")
        print(f"   ✅ Cut-in wind speed: {CUT_IN_WIND_SPEED} m/s")
        print(f"   ✅ Operational states: {len(OPERATIONAL_STATES)} defined")
        print(f"   ✅ Required columns: {len(REQUIRED_COLUMNS)} specified")

        return True

    except Exception as e:
        print(f"   ❌ Configuration test failed: {str(e)}")
        return False


def test_helper_functions():
    """Test helper functions."""
    print("🧪 Testing Helper Functions...")

    try:
        from src.utils.helpers import (
            format_timestamp, format_duration, calculate_availability
        )
        from datetime import datetime

        # Test timestamp formatting
        test_time = datetime.now()
        formatted = format_timestamp(test_time)
        print(f"   ✅ Timestamp formatting: {formatted}")

        # Test duration formatting
        duration_str = format_duration(3665)  # 1 hour, 1 minute, 5 seconds
        print(f"   ✅ Duration formatting: {duration_str}")

        # Test availability calculation with sample data
        if os.path.exists('sample_turbine_data.pkl'):
            with open('sample_turbine_data.pkl', 'rb') as f:
                sample_data = pickle.load(f)

            # Add dummy operational state for testing
            sample_data['operational_state'] = 'PRODUCING'
            availability = calculate_availability(sample_data.head(100))
            print(f"   ✅ Availability calculation: {availability.get('producing_pct', 0):.1f}% producing")

        return True

    except Exception as e:
        print(f"   ❌ Helper functions test failed: {str(e)}")
        return False


def test_layouts():
    """Test layout creation."""
    print("🧪 Testing Layouts...")

    try:
        from src.layouts.main_dashboard import create_main_dashboard_layout
        from src.layouts.investigation_panel import create_investigation_panel_layout

        # Test main dashboard layout
        create_main_dashboard_layout()
        print("   ✅ Main dashboard layout created")

        # Test investigation panel layout
        create_investigation_panel_layout("WTG_001")
        print("   ✅ Investigation panel layout created")

        return True

    except Exception as e:
        print(f"   ❌ Layout test failed: {str(e)}")
        return False


def run_all_tests():
    """Run all tests."""
    print("🚀 Running Wind Farm Turbine Investigation Application Tests\n")

    tests = [
        ("Configuration", test_configuration),
        ("Data Loader", test_data_loader),
        ("Operational State Classifier", test_operational_state_classifier),
        ("Helper Functions", test_helper_functions),
        ("Layouts", test_layouts)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"   ❌ {test_name} test crashed: {str(e)}\n")

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print("\n🚀 To start the application:")
        print("   1. Run: python app.py")
        print("   2. Open: http://localhost:8050")
        print("   3. Upload the sample data files to test the features")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")

    return passed == total


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
